# Logs
logs/n8n_builder.log
*.log

# Sensitive configuration files
.env
.env.local
.env.production
.env.development
config.ps1

# Personal/Local files
**/config.ps1
**/.env
**/.env.local

# IDE and OS files
.vscode/settings.json
.idea/
*.swp
*.swo
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*.tmp

# Windows-specific files
nul
CON
PRN
AUX

# nGrok configuration (contains auth tokens)
ngrok.yml
ngrok-config*.yml
**/ngrok.yml
**/ngrok-config*.yml

# Added by pre-commit cleanup
*.backup.*
*.cover
*.egg
*.egg-info/
*.pyc
*.pyd
*.pyo
*.temp
*~
.Python
.cache/
.coverage
.coverage.*
.eggs/
.hypothesis/
.installed.cfg
.pytest_cache/
.vscode/
ENV/
__pycache__
build/
coverage.xml
desktop.ini
develop-eggs/
dist/
downloads/
eggs/
env/
htmlcov/
lib/
lib64/
logs/
node_modules/
npm-debug.log*
parts/
project_analysis_report.json
safe_project_analysis_*.json
safe_project_analysis_*.md
sdist/
var/
venv/
wheels/
yarn-debug.log*
yarn-error.log*

# CRITICAL: Private/Advanced Components (NEVER COMMIT TO PUBLIC)
Self_Healer/
Self-Healer/
KnowledgeBase/
advanced_systems/

# Private configuration files
config_private.yaml
healer_config.yaml
knowledgebase_config.yaml

# Private test results and analysis
*_private_*
*_advanced_*
pre_commit_cleanup_summary.json

# Cleanup and deployment scripts (contain private info)
cleanup_duplicate_folder.ps1
cleanup_self_healer.ps1
test_smart_logging.py
