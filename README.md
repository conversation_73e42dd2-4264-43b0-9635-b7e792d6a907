# N8N_Builder: AI-Powered Workflow Automation

🤖 **Transform plain English into powerful N8N workflows using AI**

> **Built with Augment Code** - Demonstrating advanced AI-assisted development capabilities

## 🚀 Quick Start (Choose Your Speed)

| Time Available | Start Here | What You'll Get |
|----------------|------------|-----------------|
| **2 minutes** | [⚡ Lightning Start](LIGHTNING_START.md) | Working system, no explanations |
| **15 minutes** | [📖 Getting Started](GETTING_STARTED.md) | Understanding + customization |
| **30 minutes** | [🎯 First Workflow](Documentation/guides/FIRST_WORKFLOW.md) | Complete workflow tutorial |

## 🏗️ How It Works

```mermaid
graph LR
    A[Describe in English] --> B[AI Generates JSON]
    B --> C[Import to n8n]
    C --> D[Workflow Runs]

    classDef process fill:#e8f5e8
    class A,B,C,D process
```

**Complete System:**
1. **🤖 N8N_Builder** (this repo) - AI workflow generator
2. **🔧 Self-Healer** - Automatic error detection and resolution
3. **🐳 n8n-docker** - Production execution environment
4. **🔄 Integration** - Seamless workflow transfer

## ✨ What You Can Build

**💡 Example Automations:**
- *"Send me an email when a new file is uploaded to my folder"*
- *"Post to Twitter when I publish a new blog article"*
- *"Convert CSV files to JSON and send to a webhook"*
- *"Alert me when my website goes down"*
- *"Send welcome emails to new customers"*

## 🎯 Key Features

- **🤖 AI-Powered**: Convert plain English to n8n workflows
- **🔍 Smart Research**: Real-time n8n documentation lookup
- **⚡ Dual APIs**: Standard REST + AG-UI Protocol
- **✅ Validation**: Ensures workflows meet n8n standards
- **🔄 Iteration**: Modify existing workflows easily
- **🌐 Web Interface**: User-friendly workflow generation
- **🏭 Production Ready**: Complete Docker execution environment
- **🔧 Self-Healer**: Automatic error detection and resolution system
- **📊 Dashboard**: Real-time monitoring and healing session tracking
- **🗄️ Database Integration**: KnowledgeBase with stored procedures for optimal performance
- **📋 24-Hour Log Rotation**: Automated log management with compression and retention
- **🧪 Comprehensive Testing**: System health monitoring and validation suite
- **🧹 Smart Cleanup**: Automated project maintenance and file optimization
- **🛡️ Auto-Recovery**: Intelligent system startup and port management

## 🔧 System Health & Maintenance

N8N Builder includes comprehensive system management tools for optimal performance:

### **🏥 Health Monitoring**
```bash
# Run complete system health check
python tests/run_system_tests.py

# Check individual components
python tests/test_system_health.py
python tests/test_stored_procedures.py
```

### **📋 Log Management**
```bash
# Set up 24-hour log rotation
.\Scripts\Setup-LogRotation.ps1 -Setup

# Check log status
.\Scripts\Setup-LogRotation.ps1 -Status

# Clean up old logs
.\Scripts\Setup-LogRotation.ps1 -Cleanup 30
```

### **🧹 Project Maintenance**
```bash
# Analyze project files (safe)
.\Scripts\Cleanup-Project.ps1 -Analyze

# Preview cleanup operations
.\Scripts\Cleanup-Project.ps1 -DryRun

# Execute cleanup (archives files safely)
.\Scripts\Cleanup-Project.ps1 -Execute
```

**Key Features:**
- **🏥 System Health**: Database, network, and Self-Healer validation
- **📋 Log Rotation**: 24-hour rotation with compression and 30-day retention
- **🧹 Smart Cleanup**: Reduces 5000+ files to manageable levels
- **🗄️ Database Optimization**: Stored procedures for enhanced performance
- **🔒 Safe Operations**: Files archived, not deleted, with complete audit trail

## 📚 Documentation

### 🎯 **Start Here**
- **📖 [Complete Documentation](Documentation/README.md)** - Master guide
- **🔧 [Troubleshooting](Documentation/TROUBLESHOOTING.md)** - Fix common issues

### 🔧 **For Developers**
- **📚 [API Documentation](Documentation/api/API_DOCUMENTATION.md)** - Complete reference
- **🏗️ [Technical Architecture](Documentation/technical/DOCUMENTATION.md)** - System design

### 🐳 **n8n-docker Setup**
- **⚡ [Lightning Start](n8n-docker/LIGHTNING_START.md)** - 2-minute setup
- **📖 [Complete Guide](n8n-docker/Documentation/README.md)** - Full reference

### 🤖 **Advanced Features**
- **🔧 [Self-Healer](Self_Healer/Documentation/README.md)** - Automatic error resolution

## 🚀 Recent Updates

### **🔧 System Improvements (Latest)**
- ✅ **Database Architecture Enhancement** - Migrated to stored procedures for optimal performance
- ✅ **24-Hour Log Rotation** - Automated log management with compression and retention
- ✅ **Comprehensive Testing Suite** - System health monitoring and validation framework
- ✅ **Smart Project Cleanup** - Automated maintenance reducing 5000+ files to manageable levels
- ✅ **Enhanced MCP Database Tool** - Multiple result set support and better error handling

### **🤖 AI & Integration**
- ✅ **Enhanced AI Generation** - Improved workflow quality and reliability
- ✅ **MCP Research Integration** - Real-time n8n documentation lookup
- ✅ **Better Error Handling** - Robust retry logic and fallback strategies
- ✅ **Self-Healer KnowledgeBase** - Advanced error resolution with stored procedures

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new features
4. Update documentation
5. Submit a pull request

## 📄 License

MIT License - See LICENSE file for details

---

**🎉 Ready to automate your workflows with AI?** Start with [⚡ Lightning Start](LIGHTNING_START.md) and be running in 2 minutes!